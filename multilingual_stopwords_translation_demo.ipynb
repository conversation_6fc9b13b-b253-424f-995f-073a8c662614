{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multilingual Stopwords Dataset with English Translation\n", "\n", "This notebook demonstrates the complete process of creating and using a comprehensive multilingual stopwords dataset with English translations, optimized for Indonesian social media text analysis.\n", "\n", "## Overview\n", "- **Total entries**: 2,386\n", "- **Languages**: Indonesian (formal/colloquial), English, Javanese, Sundanese\n", "- **English coverage**: 46.6% (1,113 translations)\n", "- **Translation method**: Dictionary-based mapping with manual curation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 20)\n", "\n", "print(\"📚 Libraries loaded successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the translated multilingual stopwords dataset\n", "df = pd.read_csv('multilingual_stopwords_dict_only.csv')\n", "\n", "print(f\"📊 Dataset loaded successfully!\")\n", "print(f\"Total entries: {len(df):,}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "print(\"\\n📋 First 5 rows:\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Statistics and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate language coverage statistics\n", "def calculate_coverage(df):\n", "    total_entries = len(df)\n", "    coverage_stats = {}\n", "    \n", "    for col in df.columns:\n", "        non_empty = (df[col].notna() & (df[col] != '')).sum()\n", "        percentage = (non_empty / total_entries) * 100\n", "        coverage_stats[col] = {\n", "            'count': non_empty,\n", "            'percentage': percentage\n", "        }\n", "    \n", "    return coverage_stats\n", "\n", "coverage = calculate_coverage(df)\n", "\n", "print(\"🌍 Language Coverage Statistics:\")\n", "print(\"=\" * 50)\n", "for lang, stats in coverage.items():\n", "    lang_name = {\n", "        'en': 'English',\n", "        'id': 'Indonesian (Colloquial)',\n", "        'jv': 'Javanese',\n", "        'su': 'Sundanese',\n", "        'formal_id': 'Indonesian (Formal)'\n", "    }.get(lang, lang)\n", "    \n", "    print(f\"{lang_name:25}: {stats['count']:4,} entries ({stats['percentage']:5.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize language coverage\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Prepare data for visualization\n", "languages = ['English', 'Indonesian\\n(Colloquial)', 'Javanese', 'Sundanese', 'Indonesian\\n(Formal)']\n", "counts = [coverage[col]['count'] for col in ['en', 'id', 'jv', 'su', 'formal_id']]\n", "percentages = [coverage[col]['percentage'] for col in ['en', 'id', 'jv', 'su', 'formal_id']]\n", "\n", "# Create bar plot\n", "bars = plt.bar(languages, counts, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])\n", "\n", "# Add percentage labels on bars\n", "for bar, pct in zip(bars, percentages):\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 20,\n", "             f'{pct:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.title('Multilingual Stopwords Dataset - Language Coverage', fontsize=16, fontweight='bold')\n", "plt.ylabel('Number of Entries', fontsize=12)\n", "plt.xlabel('Languages', fontsize=12)\n", "plt.xticks(rotation=45)\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"📈 English coverage improved significantly with {coverage['en']['count']:,} translations!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Translation Quality Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze translation quality by categories\n", "def analyze_translation_categories(df):\n", "    # Define categories based on English translations\n", "    categories = {\n", "        'Pronouns': ['i', 'you', 'he', 'she', 'they', 'we', 'it'],\n", "        'Question Words': ['what', 'how', 'why', 'when', 'where', 'who', 'which'],\n", "        'Intensifiers': ['very', 'too', 'quite', 'enough', 'most', 'more', 'less', 'much'],\n", "        'Demonstratives': ['this', 'that', 'like this', 'like that'],\n", "        'Conjunctions': ['and', 'or', 'but', 'because', 'when', 'if', 'while'],\n", "        'Prepositions': ['in', 'on', 'at', 'to', 'from', 'with', 'for', 'by'],\n", "        'Modal Verbs': ['can', 'may', 'must', 'should', 'will', 'would'],\n", "        'Time Words': ['now', 'later', 'yesterday', 'tomorrow', 'today', 'before', 'after']\n", "    }\n", "    \n", "    category_stats = {}\n", "    \n", "    for category, words in categories.items():\n", "        matches = df[df['en'].isin(words)]\n", "        category_stats[category] = len(matches)\n", "    \n", "    return category_stats\n", "\n", "category_stats = analyze_translation_categories(df)\n", "\n", "print(\"🎯 Translation Quality by Category:\")\n", "print(\"=\" * 40)\n", "for category, count in category_stats.items():\n", "    print(f\"{category:15}: {count:3d} entries\")\n", "\n", "total_categorized = sum(category_stats.values())\n", "print(f\"\\n📊 Total categorized translations: {total_categorized}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show sample high-quality translations\n", "print(\"✨ Sample High-Quality Translations:\")\n", "print(\"=\" * 50)\n", "\n", "# Get some representative examples\n", "sample_categories = {\n", "    'Pronouns': ['i', 'you', 'he', 'they', 'we'],\n", "    'Question Words': ['what', 'how', 'why', 'when', 'where'],\n", "    'Intensifiers': ['very', 'too', 'enough'],\n", "    'Demonstratives': ['this', 'that', 'like this', 'like that']\n", "}\n", "\n", "for category, words in sample_categories.items():\n", "    print(f\"\\n{category}:\")\n", "    for word in words:\n", "        matches = df[df['en'] == word]\n", "        if not matches.empty:\n", "            # Get first match\n", "            row = matches.iloc[0]\n", "            indonesian = row['formal_id'] if pd.notna(row['formal_id']) else row['id']\n", "            if pd.notna(indonesian):\n", "                print(f\"  {indonesian:12} → {word}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Translation Dictionary Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show the comprehensive Indonesian-English dictionary used for translation\n", "INDONESIAN_ENGLISH_DICT = {\n", "    # Pronouns\n", "    'saya': 'i', 'aku': 'i', 'kamu': 'you', 'dia': 'he', 'mereka': 'they',\n", "    'kita': 'we', 'kami': 'we', 'kalian': 'you', 'beliau': 'he',\n", "    \n", "    # Function words\n", "    'yang': 'which', 'dengan': 'with', 'untuk': 'for', 'dari': 'from',\n", "    'pada': 'on', 'dalam': 'in', 'oleh': 'by', 'ke': 'to', 'di': 'in',\n", "    \n", "    # Intensifiers and adverbs\n", "    'banget': 'very', 'sangat': 'very', 'sekali': 'very', 'agak': 'quite',\n", "    'cukup': 'enough', 'terlalu': 'too', 'lebih': 'more', 'kurang': 'less',\n", "    \n", "    # Question words\n", "    'apa': 'what', 'bagaimana': 'how', 'kenapa': 'why', 'kapan': 'when',\n", "    'dimana': 'where', 'siapa': 'who', 'mana': 'which',\n", "    \n", "    # Demonstratives\n", "    'ini': 'this', 'itu': 'that', 'begitu': 'like that', 'begini': 'like this',\n", "    'seperti': 'like', 'kayak': 'like', 'kaya': 'like',\n", "    \n", "    # Conjunctions\n", "    'dan': 'and', 'atau': 'or', 'tetapi': 'but', 'karena': 'because',\n", "    'jika': 'if', 'ketika': 'when', 'saat': 'when', 'waktu': 'when',\n", "    \n", "    # Modal verbs\n", "    'bisa': 'can', 'dapat': 'can', 'mau': 'want', 'ingin': 'want',\n", "    'harus': 'must', 'perlu': 'need', 'boleh': 'may',\n", "    \n", "    # Social media slang\n", "    'gitu': 'like that', 'gini': 'like this', 'gimana': 'how',\n", "    'bgt': 'very', 'bngt': 'very', 'yg': 'which', 'dgn': 'with',\n", "    \n", "    # Particles (mapped to empty string for removal)\n", "    'lah': '', 'kah': '', 'pun': '', 'sih': '', 'dong': '', 'kok': '',\n", "    'deh': '', 'tuh': '', 'nih': ''\n", "}\n", "\n", "print(f\"📖 Translation Dictionary Statistics:\")\n", "print(f\"Total mappings: {len(INDONESIAN_ENGLISH_DICT)}\")\n", "print(f\"Non-empty translations: {len([v for v in INDONESIAN_ENGLISH_DICT.values() if v])}\")\n", "print(f\"Particle removals: {len([v for v in INDONESIAN_ENGLISH_DICT.values() if not v])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demonstrate the translation process\n", "def translate_with_dictionary(text, dictionary):\n", "    \"\"\"Translate Indonesian text using the predefined dictionary\"\"\"\n", "    words = text.lower().split()\n", "    translated_words = []\n", "    \n", "    for word in words:\n", "        if word in dictionary:\n", "            translation = dictionary[word]\n", "            if translation:  # Only add non-empty translations\n", "                translated_words.append(translation)\n", "        else:\n", "            translated_words.append(word)  # Keep original if no translation\n", "    \n", "    return ' '.join(translated_words)\n", "\n", "# Test translation examples\n", "test_sentences = [\n", "    \"saya sangat suka dengan makanan ini\",\n", "    \"dia banget pintar dalam matematika\",\n", "    \"aku mau pergi ke sana dong\",\n", "    \"bagaimana cara untuk membuat kue yang enak\",\n", "    \"mereka bisa datang kapan saja\"\n", "]\n", "\n", "print(\"🔄 Translation Examples:\")\n", "print(\"=\" * 60)\n", "for sentence in test_sentences:\n", "    translated = translate_with_dictionary(sentence, INDONESIAN_ENGLISH_DICT)\n", "    print(f\"Original : {sentence}\")\n", "    print(f\"Translated: {translated}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Practical Applications"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create stopword removal function\n", "def create_stopword_remover(language='id'):\n", "    \"\"\"Create a stopword removal function for specified language\"\"\"\n", "    \n", "    if language == 'id':\n", "        # Combine colloquial and formal Indonesian\n", "        stopwords = set()\n", "        stopwords.update(df['id'].dropna().str.lower().tolist())\n", "        stopwords.update(df['formal_id'].dropna().str.lower().tolist())\n", "    elif language == 'en':\n", "        stopwords = set(df['en'].dropna().str.lower().tolist())\n", "    elif language == 'jv':\n", "        stopwords = set(df['jv'].dropna().str.lower().tolist())\n", "    elif language == 'su':\n", "        stopwords = set(df['su'].dropna().str.lower().tolist())\n", "    else:\n", "        raise ValueError(f\"Unsupported language: {language}\")\n", "    \n", "    def remove_stopwords(text):\n", "        words = text.lower().split()\n", "        filtered_words = [word for word in words if word not in stopwords]\n", "        return ' '.join(filtered_words)\n", "    \n", "    return remove_stopwords, stopwords\n", "\n", "# Create stopword removers for different languages\n", "id_remover, id_stopwords = create_stopword_remover('id')\n", "en_remover, en_stopwords = create_stopword_remover('en')\n", "\n", "print(f\"📊 Stopword Statistics:\")\n", "print(f\"Indonesian stopwords: {len(id_stopwords):,}\")\n", "print(f\"English stopwords: {len(en_stopwords):,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demonstrate social media text preprocessing\n", "social_media_texts = [\n", "    \"wah banget sih kak, aku tuh pengen banget ke sana dong!\",\n", "    \"gimana caranya untuk daftar kuliah yang bagus?\",\n", "    \"dia itu sangat pintar dalam bidang teknologi loh\",\n", "    \"aku mau beli makanan yang enak di warung itu\",\n", "    \"mereka bisa datang ke acara kita nanti sore\"\n", "]\n", "\n", "print(\"🔧 Social Media Text Preprocessing:\")\n", "print(\"=\" * 70)\n", "\n", "for i, text in enumerate(social_media_texts, 1):\n", "    cleaned = id_remover(text)\n", "    print(f\"Text {i}:\")\n", "    print(f\"  Original: {text}\")\n", "    print(f\"  Cleaned : {cleaned}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cross-language mapping demonstration\n", "def get_cross_language_mapping(indonesian_word):\n", "    \"\"\"Get cross-language mappings for an Indonesian word\"\"\"\n", "    matches = df[\n", "        (df['id'].str.lower() == indonesian_word.lower()) | \n", "        (df['formal_id'].str.lower() == indonesian_word.lower())\n", "    ]\n", "    \n", "    if matches.empty:\n", "        return None\n", "    \n", "    row = matches.iloc[0]\n", "    mapping = {}\n", "    \n", "    for lang in ['en', 'jv', 'su']:\n", "        if pd.notna(row[lang]) and row[lang] != '':\n", "            mapping[lang] = row[lang]\n", "    \n", "    return mapping\n", "\n", "# Test cross-language mapping\n", "test_words = ['saya', 'yang', 'dengan', 'banget', 'apa', 'ini']\n", "\n", "print(\"🌐 Cross-Language Mapping Examples:\")\n", "print(\"=\" * 50)\n", "\n", "for word in test_words:\n", "    mapping = get_cross_language_mapping(word)\n", "    if mapping:\n", "        print(f\"Indonesian: {word}\")\n", "        for lang, translation in mapping.items():\n", "            lang_name = {'en': 'English', 'jv': 'Javanese', 'su': 'Sundanese'}[lang]\n", "            print(f\"  {lang_name:10}: {translation}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Sentiment Analysis Application"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simulate sentiment analysis with stopword removal\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.metrics import classification_report\n", "\n", "# Sample Indonesian social media data for sentiment analysis\n", "sample_data = [\n", "    (\"film ini sangat bagus dan menarik sekali\", \"positive\"),\n", "    (\"makanan di restoran itu enak banget\", \"positive\"),\n", "    (\"pelayanan yang buruk dan mengecewakan\", \"negative\"),\n", "    (\"harga terlalu mahal untuk kualitas segitu\", \"negative\"),\n", "    (\"tempat wisata yang indah dan nyaman\", \"positive\"),\n", "    (\"produk ini tidak sesuai dengan ekspektasi\", \"negative\"),\n", "    (\"pengalaman berbelanja yang menyenangkan\", \"positive\"),\n", "    (\"kualitas produk sangat mengecewakan sekali\", \"negative\"),\n", "    (\"rekomendasi tempat makan yang enak\", \"positive\"),\n", "    (\"pelayanan lambat dan tidak profesional\", \"negative\")\n", "]\n", "\n", "texts = [item[0] for item in sample_data]\n", "labels = [item[1] for item in sample_data]\n", "\n", "print(\"📊 Sample Data for Sentiment Analysis:\")\n", "for i, (text, label) in enumerate(sample_data[:5], 1):\n", "    print(f\"{i}. [{label.upper()}] {text}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare sentiment analysis with and without stopword removal\n", "def compare_sentiment_analysis(texts, labels, stopwords=None):\n", "    \"\"\"Compare sentiment analysis performance with/without stopwords\"\"\"\n", "    \n", "    # Preprocess texts\n", "    if stopwords:\n", "        processed_texts = [id_remover(text) for text in texts]\n", "        print(\"🔧 Using stopword removal\")\n", "    else:\n", "        processed_texts = texts\n", "        print(\"📝 Without stopword removal\")\n", "    \n", "    # Create TF-IDF vectorizer\n", "    vectorizer = TfidfVectorizer(max_features=100, ngram_range=(1, 2))\n", "    X = vectorizer.fit_transform(processed_texts)\n", "    \n", "    # Show feature names\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    print(f\"\\n📋 Top 10 features: {list(feature_names[:10])}\")\n", "    print(f\"Total features: {len(feature_names)}\")\n", "    \n", "    return X, vectorizer\n", "\n", "print(\"Comparison: Sentiment Analysis Feature Extraction\")\n", "print(\"=\" * 60)\n", "\n", "# Without stopword removal\n", "X_without, vec_without = compare_sentiment_analysis(texts, labels, stopwords=None)\n", "\n", "print(\"\\n\" + \"-\" * 40)\n", "\n", "# With stopword removal\n", "X_with, vec_with = compare_sentiment_analysis(texts, labels, stopwords=id_stopwords)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Export and Usage Guidelines"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create usage examples for different frameworks\n", "usage_examples = {\n", "    'pandas': '''\n", "# Load and use with pandas\n", "import pandas as pd\n", "df = pd.read_csv('multilingual_stopwords_dict_only.csv')\n", "indonesian_stopwords = set(df['id'].dropna().str.lower().tolist())\n", "''',\n", "    \n", "    'sklearn': '''\n", "# Use with scikit-learn TfidfVectorizer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "vectorizer = TfidfVectorizer(stop_words=list(indonesian_stopwords))\n", "''',\n", "    \n", "    'nltk': '''\n", "# Use with NLTK\n", "import nltk\n", "from nltk.corpus import stopwords\n", "# Add custom Indonesian stopwords to NLTK\n", "nltk_stopwords = set(stopwords.words('english'))\n", "nltk_stopwords.update(indonesian_stopwords)\n", "''',\n", "    \n", "    'spacy': '''\n", "# Use with spaCy (custom component)\n", "import spacy\n", "nlp = spacy.blank('id')  # Indonesian language model\n", "nlp.Defaults.stop_words.update(indonesian_stopwords)\n", "'''\n", "}\n", "\n", "print(\"📚 Framework Integration Examples:\")\n", "print(\"=\" * 50)\n", "\n", "for framework, code in usage_examples.items():\n", "    print(f\"\\n{framework.upper()}:\")\n", "    print(code.strip())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary and recommendations\n", "print(\"🎯 MULTILINGUAL STOPWORDS DATASET SUMMARY\")\n", "print(\"=\" * 60)\n", "print(f\"📊 Total entries: {len(df):,}\")\n", "print(f\"🌍 Languages supported: 5 (Indonesian formal/colloquial, English, Javanese, Sundanese)\")\n", "print(f\"🔤 English translations: {coverage['en']['count']:,} ({coverage['en']['percentage']:.1f}%)\")\n", "print(f\"📈 Translation improvement: +{coverage['en']['count'] - 245:,} new English entries\")\n", "\n", "print(\"\\n✨ KEY FEATURES:\")\n", "features = [\n", "    \"✓ Comprehensive Indonesian social media slang coverage\",\n", "    \"✓ High-quality manual translations for core stopwords\",\n", "    \"✓ Support for formal and colloquial Indonesian variants\",\n", "    \"✓ Regional language support (Javanese, Sundanese)\",\n", "    \"✓ Cross-language mapping capabilities\",\n", "    \"✓ Optimized for sentiment analysis applications\",\n", "    \"✓ Ready for integration with popular NLP frameworks\"\n", "]\n", "\n", "for feature in features:\n", "    print(f\"  {feature}\")\n", "\n", "print(\"\\n🚀 RECOMMENDED APPLICATIONS:\")\n", "applications = [\n", "    \"• Indonesian social media sentiment analysis\",\n", "    \"• Cross-language text classification\",\n", "    \"• Multilingual information retrieval\",\n", "    \"• Regional Indonesian language processing\",\n", "    \"• Social media monitoring and analytics\"\n", "]\n", "\n", "for app in applications:\n", "    print(f\"  {app}\")\n", "\n", "print(\"\\n📁 FILES CREATED:\")\n", "files = [\n", "    \"• multilingual_stopwords_dict_only.csv - Final translated dataset\",\n", "    \"• translate_with_dictionary.py - Translation script\",\n", "    \"• README_Translated_Multilingual_Stopwords.md - Documentation\",\n", "    \"• multilingual_stopwords_translation_demo.ipynb - This notebook\"\n", "]\n", "\n", "for file in files:\n", "    print(f\"  {file}\")\n", "\n", "print(\"\\n🎉 Translation project completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}